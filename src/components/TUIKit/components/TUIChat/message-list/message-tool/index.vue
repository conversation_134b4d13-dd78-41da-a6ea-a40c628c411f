<template>
  <div v-if="!isAllActionItemInvalid && !messageItem.hasRiskContent && isMessageSuccessful(messageItem)" ref="messageToolDom" :class="['dialog-item', !isPC ? 'dialog-item-h5' : 'dialog-item-web']">
    <div
      ref="dialogItemListRef"
      class="dialog-item-list"
      :class="[!isPC ? 'dialog-item-list-h5' : 'dialog-item-list-web', { 'has-arrow-up': arrowDirection === 'up', 'has-arrow-down': arrowDirection === 'down' }]"
    >
      <template v-for="(item, index) in actionItems">
        <div v-if="item.renderCondition()" :key="item.key" class="list-item" @click="getFunction(index)" @mousedown="beforeCopy(item.key)">
          <div class="list-item-icon">
            <img :src="item.iconUrl" />
          </div>
          <span class="list-item-text">{{ item.text }}</span>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
import TUIChatEngine, { TUIStore, StoreName, TUITranslateService, IMessageModel, TUIChatService } from '@tencentcloud/chat-uikit-engine'
import { TUIGlobal } from '@tencentcloud/universal-api'
import { ElMessage } from 'element-plus'
import { ref, watchEffect, computed, onMounted, onUnmounted, nextTick } from '../../../../adapter-vue'
import Icon from '../../../common/Icon.vue'
import { Toast, TOAST_TYPE } from '../../../common/Toast/index'
// import delIcon from '../../../../assets/icon/msg-del.svg'
// import copyIcon from '../../../../assets/icon/msg-copy.svg'
// import quoteIcon from '../../../../assets/icon/msg-quote.svg'
// import revokeIcon from '../../../../assets/icon/msg-revoke.svg'
import forwardIcon from '../../../../assets/icon/msg-forward.svg'
import translateIcon from '../../../../assets/icon/translate.svg'
// import multipleSelectIcon from '../../../../assets/icon/multiple-select.svg'
import convertText from '../../../../assets/icon/convertText_zh.svg'
import { enableSampleTaskStatus } from '../../../../utils/enableSampleTaskStatus'
import { transformTextWithKeysToEmojiNames } from '../../emoji-config'
import { isH5, isPC, isUniFrameWork } from '../../../../utils/env'
import { ITranslateInfo, IConvertInfo } from '../../../../interface'
import TUIChatConfig from '../../config'
import useChatStore from '@/store/modules/chat'
// uni-app conditional compilation will not run the following code
// #ifndef APP || APP-PLUS || MP || H5
import CopyManager from '../../utils/copy'
import copyIcon from '@/assets/images/bubble-tool/copy.png'
import quoteIcon from '@/assets/images/bubble-tool/quote.png'
import deleteIcon from '@/assets/images/bubble-tool/delete.png'
import selectTextIcon from '@/assets/images/bubble-tool/select-text.png'
import multipleSelectIcon from '@/assets/images/bubble-tool/multiple-select.png'
import eliminateIcon from '@/assets/images/bubble-tool/eliminate.png'
import redrawingIcon from '@/assets/images/bubble-tool/redrawing.png'
import mattingIcon from '@/assets/images/bubble-tool/matting.png'
// #endif
import { addMessageToQuoteList } from '@/utils/messageUtils'

interface IProps {
  messageItem: IMessageModel
  isMultipleSelectMode: boolean
}

interface IEmits {
  (key: 'toggleMultipleSelectMode'): void
  (key: 'closeMessageTool'): void // 新增：通知父组件关闭工具栏
}

const emits = defineEmits<IEmits>()
const props = withDefaults(defineProps<IProps>(), {
  isMultipleSelectMode: false,
  messageItem: () => ({} as IMessageModel),
})
const featureConfig = TUIChatConfig.getFeatureConfig()

const TYPES = TUIChatEngine.TYPES
const chatStore = useChatStore()
const actionItems = ref([
  {
    key: 'copy',
    iconUrl: copyIcon,
    renderCondition() {
      const messageData = message.value

      if (messageData?.from.includes('agent')) {
        const payload = JSON.parse(messageData?.payload?.data)
        if (payload?.text) {
          return true
        } else {
          return false
        }
      } else {
        if (messageData?.type === TYPES.MSG_TEXT) {
          return true
        } else {
          if (messageData?.type === 'TIMCustomElem') {
            const payload = JSON.parse(messageData?.payload?.data)
            if (payload?.content?.data?.text) {
              return true
            } else {
              return false
            }
          } else {
            return false
          }
        }
      }
    },
    clickEvent: copyMessage,
    text: '复制',
  },
  {
    key: 'quote',
    iconUrl: quoteIcon,
    renderCondition() {
      if (!featureConfig.QuoteMessage || !message.value) return false
      const _message = TUIStore.getMessageModel(message.value.ID)
      const messageData = message.value?.payload
      if (messageData?.data) {
        try {
          const data = JSON.parse(messageData.data)

          if (data.businessID === 'ai_message' && Array.isArray(data?.tips) && data.tips.length > 0 && !data?.text?.length) {
            return false
          }
          if (data?.searchRespList?.length > 0) {
            return false
          }
          if (data?.images?.length > 1) {
            return false
          }
        } catch (error) {}
      }
      return message.value.status === 'success' && !_message.getSignalingInfo()
    },
    clickEvent: handleQuote,
    text: '引用',
  },
  {
    key: 'multi-select',
    iconUrl: multipleSelectIcon,
    renderCondition() {
      if (!featureConfig.MultiSelection || !message.value) return false
      const messageData = message.value?.payload
      if (messageData?.data) {
        try {
          const data = JSON.parse(messageData.data)
          if (data.businessID === 'ai_message' && Array.isArray(data?.tips) && data.tips.length > 0 && !data?.text?.length) {
            return false
          }
        } catch (error) {}
      }

      return message.value.status === 'success'
    },
    clickEvent: multipleSelectMessage,
    text: '多选',
  },
  {
    key: 'select-text',
    iconUrl: selectTextIcon,
    renderCondition() {
      const messageData = message.value

      if (messageData?.from.includes('agent')) {
        const payload = JSON.parse(messageData?.payload?.data)
        if (payload?.text) {
          return true
        } else {
          return false
        }
      } else {
        if (messageData?.type === TYPES.MSG_TEXT) {
          return true
        } else {
          if (messageData?.type === 'TIMCustomElem') {
            const payload = JSON.parse(messageData?.payload?.data)
            if (payload?.content?.data?.text) {
              return true
            } else {
              return false
            }
          } else {
            return false
          }
        }
      }
    },
    clickEvent: selectText,
    text: '选文字',
  },

  {
    key: 'matting',
    iconUrl: mattingIcon,
    renderCondition() {
      const messageData = message.value
      // Agent 消息判断逻辑
      if (messageData?.from?.includes('agent')) {
        try {
          const payload = JSON.parse(messageData?.payload?.data || '{}')
          // 判断 images 数组长度是否等于1
          console.log('🚀 ~ renderCondition ~ payload:', payload)
          return Array.isArray(payload?.images) && payload.images.length === 1
        } catch (error) {
          return false
        }
      } else {
        // 非 Agent 消息
        if (messageData?.type === 'TIMCustomElem') {
          const payload = JSON.parse(messageData?.payload?.data)
          console.log('🚀 ~ renderCondition ~ payload:', payload)
          if (payload?.content?.name === 'image') {
            return true
          } else {
            return false
          }
        } else {
          return false
        }
      }
    },
    clickEvent: mattingImage,
    text: '抠图',
  },
  {
    key: 'redrawing',
    iconUrl: redrawingIcon,
    renderCondition() {
      const messageData = message.value

      // Agent 消息判断逻辑
      if (messageData?.from?.includes('agent')) {
        try {
          const payload = JSON.parse(messageData?.payload?.data || '{}')
          // 判断 images 数组长度是否等于1
          return Array.isArray(payload?.images) && payload.images.length === 1
        } catch (error) {
          return false
        }
      } else {
        // 非 Agent 消息：判断是否为图片类型
        if (messageData?.type === 'TIMCustomElem') {
          const payload = JSON.parse(messageData?.payload?.data)
          if (payload?.content?.name === 'image') {
            return true
          } else {
            return false
          }
        } else {
          return false
        }
      }
    },
    clickEvent: redrawingImage,
    text: '局部重绘',
  },
  {
    key: 'eliminate',
    iconUrl: eliminateIcon,
    renderCondition() {
      const messageData = message.value

      // Agent 消息判断逻辑
      if (messageData?.from?.includes('agent')) {
        try {
          const payload = JSON.parse(messageData?.payload?.data || '{}')
          // 判断 images 数组长度是否等于1
          return Array.isArray(payload?.images) && payload.images.length === 1
        } catch (error) {
          return false
        }
      } else {
        // 非 Agent 消息：判断是否为图片类型
        if (messageData?.type === 'TIMCustomElem') {
          const payload = JSON.parse(messageData?.payload?.data)
          if (payload?.content?.name === 'image') {
            return true
          } else {
            return false
          }
        } else {
          return false
        }
      }
    },
    clickEvent: eliminateImage,
    text: '消除',
  },
])

const message = ref<IMessageModel>()
const messageToolDom = ref<HTMLElement>()
const dialogItemListRef = ref<HTMLElement>()
const arrowDirection = ref<'up' | 'down' | null>(null)

// 视口检测相关状态
const hasLeftViewport = ref(false) // 记录对话框是否已离开过视口（实现"一次性显示"）
const intersectionObserver = ref<IntersectionObserver | null>(null)

onMounted(() => {
  TUIStore.watch(StoreName.CHAT, {
    translateTextInfo: onMessageTranslationInfoUpdated,
    voiceToTextInfo: onMessageConvertInfoUpdated,
  })

  // 初始化箭头方向
  nextTick(() => {
    calculateArrowDirection()
    // 初始化视口检测
    initIntersectionObserver()
  })
})

onUnmounted(() => {
  TUIStore.unwatch(StoreName.CHAT, {
    translateTextInfo: onMessageTranslationInfoUpdated,
    voiceToTextInfo: onMessageConvertInfoUpdated,
  })

  // 清理视口观察器
  cleanupIntersectionObserver()
})

watchEffect(() => {
  message.value = TUIStore.getMessageModel(props.messageItem.ID)
})

// 计算箭头方向的函数
const calculateArrowDirection = () => {
  if (!messageToolDom.value || !dialogItemListRef.value) return

  nextTick(() => {
    const toolElement = messageToolDom.value
    const listElement = dialogItemListRef.value

    if (!toolElement || !listElement) return

    // 获取工具栏容器的位置信息
    const toolRect = toolElement.getBoundingClientRect()

    // 查找对应的消息体元素
    const messageElement = toolElement.closest('.message-item')
    if (!messageElement) return

    const messageBubble = messageElement.querySelector('.message-bubble')
    if (!messageBubble) return

    const messageRect = messageBubble.getBoundingClientRect()

    // 判断工具栏相对于消息体的位置
    // 如果工具栏在消息体上方，箭头指向下方
    // 如果工具栏在消息体下方，箭头指向上方
    if (toolRect.bottom <= messageRect.top) {
      // 工具栏在消息体上方
      arrowDirection.value = 'down'
    } else if (toolRect.top >= messageRect.bottom) {
      // 工具栏在消息体下方
      arrowDirection.value = 'up'
    } else {
      // 工具栏与消息体重叠或其他情况，根据CSS类判断
      const hasBottomClass = toolElement.classList.contains('message-tool-bottom')
      arrowDirection.value = hasBottomClass ? 'up' : 'down'
    }
  })
}

// 初始化视口检测观察器
const initIntersectionObserver = () => {
  if (!dialogItemListRef.value || intersectionObserver.value) return

  // 创建 Intersection Observer 实例
  intersectionObserver.value = new IntersectionObserver(
    entries => {
      entries.forEach(entry => {
        // 当元素离开视口时（isIntersecting 为 false）
        if (!entry.isIntersecting && !hasLeftViewport.value) {
          // 标记为已离开视口，实现"一次性显示"效果
          hasLeftViewport.value = true
          // 通知父组件关闭工具栏
          emits('closeMessageTool')
        }
      })
    },
    {
      // 设置根边距，可以提前触发检测
      rootMargin: '0px',
      // 当元素完全离开视口时触发
      threshold: 0,
    }
  )

  // 开始观察目标元素
  intersectionObserver.value.observe(dialogItemListRef.value)
}

// 清理视口检测观察器
const cleanupIntersectionObserver = () => {
  if (intersectionObserver.value) {
    intersectionObserver.value.disconnect()
    intersectionObserver.value = null
  }
}

const isAllActionItemInvalid = computed(() => {
  for (let i = 0; i < actionItems.value.length; ++i) {
    if (actionItems.value[i].renderCondition()) {
      return false
    }
  }
  return true
})
const isMessageSuccessful = (message: any): boolean => {
  console.log('🚀 ~ index.vue:410 ~ isMessageSuccessful ~ message:', message)
  if (!message) return false

  // Check if it's an agent message
  if (message.from?.includes('agent')) {
    try {
      const payload = JSON.parse(message.payload?.data || '{}')
      // For agent messages, check the status in payload data
      if (payload?.status !== 'SUCCESS') return false

      // Check if has search results
      if (payload?.searchRespList?.length > 0) return false

      return true
    } catch (error) {
      return false
    }
  } else {
    // For user messages, check the message status directly
    return message.status === 'success'
  }
}

// 监听工具栏显示状态变化
watchEffect(() => {
  if (!isAllActionItemInvalid.value && !props.messageItem.hasRiskContent && isMessageSuccessful(props.messageItem)) {
    calculateArrowDirection()
  }
})

function getFunction(index: number) {
  // Compatible with Vue2 and WeChat Mini Program syntax, dynamic binding is not allowed.
  actionItems.value[index].clickEvent()
}

function openMessage() {
  let url = ''
  switch (message.value?.type) {
    case TUIChatEngine.TYPES.MSG_FILE:
      url = message.value.payload.fileUrl
      break
    case TUIChatEngine.TYPES.MSG_VIDEO:
      url = message.value.payload.remoteVideoUrl
      break
    case TUIChatEngine.TYPES.MSG_IMAGE:
      url = message.value.payload.imageInfoArray[0].url
      break
  }
  window?.open(url, '_blank')
}

function revokeMessage() {
  if (!message.value) return
  const messageModel = TUIStore.getMessageModel(message.value.ID)
  messageModel
    .revokeMessage()
    .then(() => {
      enableSampleTaskStatus('revokeMessage')
    })
    .catch((error: any) => {
      // The message cannot be recalled after the time limit was reached, which is 2 minutes by default.
      if (error.code === 20016 || error.code === 10031) {
        const message = TUITranslateService.t('TUIChat.已过撤回时限')
        Toast({
          message,
          type: TOAST_TYPE.ERROR,
        })
      }
    })
}

async function copyMessage() {
  if (isUniFrameWork) {
    TUIGlobal?.setClipboardData({
      data: transformTextWithKeysToEmojiNames(message.value?.payload?.text),
    })
  } else {
    // uni-app conditional compilation will not run the following code
    // #ifndef APP || APP-PLUS || MP || H5
    // CopyManager.copySelection(message.value?.payload?.text || '')
    if (message.value?.payload?.text) {
      CopyManager.copySelection(message.value?.payload?.text)
      // ElMessage.success('复制成功!')
    } else {
      if (message.value?.payload.data) {
        const data = JSON.parse(message.value?.payload.data)
        CopyManager.copySelection(data?.text || data?.content?.data?.cmd + data?.content?.data?.text)
        // ElMessage.success('复制成功!')
      }
    }
    // #endif
  }
}

function beforeCopy(key: string) {
  // only pc support copy selection or copy full message text
  // uni-app and h5 only support copy full message text
  if (key !== 'copy' || isH5) {
    return
  }

  // uni-app conditional compilation will not run the following code
  // #ifndef APP || APP-PLUS || MP || H5
  CopyManager.saveCurrentSelection()
  // #endif
}

function forwardSingleMessage() {
  if (!message.value) return
  TUIStore.update(StoreName.CUSTOM, 'singleForwardMessageID', message.value.ID)
}

function handleQuote() {
  if (!message.value) return

  addMessageToQuoteList(message.value)
}

function mattingImage() {
  const messageData = message.value
  let src = ''
  if (messageData?.from?.includes('agent')) {
    try {
      const payload = JSON.parse(messageData?.payload?.data || '{}')
      // 判断 images 数组长度是否等于1
      console.log('🚀 ~ renderCondition ~ payload:', payload)
      src = payload?.images[0]?.url
    } catch (error) {
      return false
    }
  } else {
    // 非 Agent 消息
    if (messageData?.type === 'TIMCustomElem') {
      const payload = JSON.parse(messageData?.payload?.data)
      console.log('🚀 ~ renderCondition ~ payload:', payload)
      if (payload?.content?.name === 'image') {
        src = payload?.content?.data[0]?.url
      }
    }
  }
  chatStore.setAiCutoutsShow(true)
  chatStore.setAiCutoutsSrc(src)
}
function eliminateImage() {
  const messageData = message.value
  let src = ''
  if (messageData?.from?.includes('agent')) {
    try {
      const payload = JSON.parse(messageData?.payload?.data || '{}')
      // 判断 images 数组长度是否等于1
      console.log('🚀 ~ renderCondition ~ payload:', payload)
      src = payload?.images[0]?.url
    } catch (error) {
      return false
    }
  } else {
    // 非 Agent 消息
    if (messageData?.type === 'TIMCustomElem') {
      const payload = JSON.parse(messageData?.payload?.data)
      console.log('🚀 ~ renderCondition ~ payload:', payload)
      if (payload?.content?.name === 'image') {
        src = payload?.content?.data[0]?.url
      }
    }
  }
  chatStore.openImageEditor(src, 'edit')
}
// const handleEdit = () => {
//   // 通过store打开ImageEditor
//   const imageUrl = props.fullScreenUrl || props.src
//

// }

// const handleRedraw = () => {
//   // 通过store打开ImageEditor
//   const imageUrl = props.fullScreenUrl || props.src
//   chatStore.openImageEditor(imageUrl, 'cutouts')
//   closeDialog()
// }
function redrawingImage() {
  const messageData = message.value
  let src = ''
  if (messageData?.from?.includes('agent')) {
    try {
      const payload = JSON.parse(messageData?.payload?.data || '{}')
      // 判断 images 数组长度是否等于1
      console.log('🚀 ~ renderCondition ~ payload:', payload)
      src = payload?.images[0]?.url
    } catch (error) {
      return false
    }
  } else {
    // 非 Agent 消息
    if (messageData?.type === 'TIMCustomElem') {
      const payload = JSON.parse(messageData?.payload?.data)
      console.log('🚀 ~ renderCondition ~ payload:', payload)
      if (payload?.content?.name === 'image') {
        src = payload?.content?.data[0]?.url
      }
    }
  }
  chatStore.openImageEditor(src, 'cutouts')
}

function multipleSelectMessage() {
  emits('toggleMultipleSelectMode')
}
function selectText() {
  chatStore.setSelectMessage(message.value)
  chatStore.setSelectTextShow(true)
}
function onMessageTranslationInfoUpdated(info: Map<string, ITranslateInfo[]>) {
  if (info === undefined) return
  const translationInfoList = info.get(props.messageItem.conversationID) || []
  const idx = actionItems.value.findIndex(item => item.key === 'translate')
  for (let i = 0; i < translationInfoList.length; ++i) {
    const { messageID, visible } = translationInfoList[i]
    if (messageID === props.messageItem.ID) {
      actionItems.value[idx].text = TUITranslateService.t(visible ? 'TUIChat.隐藏' : 'TUIChat.翻译')
      actionItems.value[idx].visible = !!visible
      return
    }
  }
  actionItems.value[idx].text = TUITranslateService.t('TUIChat.翻译')
}

function onMessageConvertInfoUpdated(info: Map<string, IConvertInfo[]>) {
  if (info === undefined) return
  const convertInfoList = info.get(props.messageItem.conversationID) || []
  const idx = actionItems.value.findIndex(item => item.key === 'convert')
  for (let i = 0; i < convertInfoList.length; ++i) {
    const { messageID, visible } = convertInfoList[i]
    if (messageID === props.messageItem.ID) {
      actionItems.value[idx].text = TUITranslateService.t(visible ? 'TUIChat.隐藏' : 'TUIChat.转文字')
      actionItems.value[idx].visible = !!visible
      return
    }
  }
  actionItems.value[idx].text = TUITranslateService.t('TUIChat.转文字')
}

defineExpose({
  messageToolDom,
  dialogItemListRef,
  calculateArrowDirection,
})
</script>

<style lang="scss" scoped>
@import '../../../../assets/styles/common';

.dialog-item-web {
  border-radius: 8px;

  // padding: 12px 0;
  .dialog-item-list {
    display: flex;
    align-items: center;
    white-space: nowrap;
    flex-wrap: wrap;
    // max-width: 280px;
    // background: #2266ff;
    border-radius: 8px;
    position: relative;

    // 箭头基础样式
    &::before {
      content: '';
      position: absolute;
      width: 0;
      height: 0;
      border-style: solid;
      left: 50%;
      transform: translateX(-50%);
      z-index: 10;
      background: transparent;
      border: none;
    }

    // 向上箭头（工具栏在消息下方时）
    &.has-arrow-up::before {
      top: -6px;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-bottom: 8px solid #2266ff;
      border-top: none;
    }

    // 向下箭头（工具栏在消息上方时）
    &.has-arrow-down::before {
      bottom: -6px;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-top: 8px solid #2266ff;
      border-bottom: none;
    }

    .list-item {
      // padding: 4px 12px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;

      .list-item-icon {
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }

      .list-item-text {
        padding-left: 4px;
        font-size: 12px;
        line-height: 17px;
        color: #000;
        margin-top: 8px;
      }
    }
  }
}

.dialog-item-h5 {
  @extend .dialog-item-web;
  padding: 0;

  .dialog-item-list {
    margin: 10px;
    white-space: nowrap;
    flex-wrap: wrap;
    // max-width: 280px;
    background: #ffffff;
    height: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    position: relative;

    // H5端箭头样式
    &::before {
      content: '';
      position: absolute;
      width: 0;
      height: 0;
      border-style: solid;
      left: 50%;
      transform: translateX(-50%);
      z-index: 10;
      background: transparent;
      border: none;
    }

    // 向上箭头（工具栏在消息下方时）
    &.has-arrow-up::before {
      top: -8px;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-bottom: 8px solid #ffffff;
      border-top: none;
    }

    // 向下箭头（工具栏在消息上方时）
    &.has-arrow-down::before {
      bottom: -8px;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-top: 8px solid #ffffff;
      border-bottom: none;
    }

    .list-item {
      /* padding: 0 8px; */
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #4f4f4f;
      height: 69px;
      min-width: 58px;

      .list-item-icon {
        width: 17px;
        height: 17px;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
          vertical-align: middle;
        }
      }

      .list-item-text {
        margin-top: 8px !important;
        font-size: 12px;
        line-height: 17px;
        color: #000;
      }
    }
  }
}
</style>
